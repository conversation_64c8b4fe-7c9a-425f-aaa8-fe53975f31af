ipcp-accept-local
ipcp-accept-remote

require-pap
refuse-chap
refuse-mschap
refuse-mschap-v2

ms-dns *******
ms-dns *******
asyncmap 0
noccp
noauth
crtscts
idle 1800
mtu 1280
mru 1280
hide-password
lock
connect-delay 5000
nodefaultroute
proxyarp
lcp-echo-interval 30
lcp-echo-failure 4

# 禁用IPv6以避免协议冲突
noipv6

debug
plugin /etc/ppp/auth_softapi.so

# 允许客户端使用分配的IP地址范围
# 这解决了 "P<PERSON> is not authorized to use remote address" 错误
noipdefault
ipcp-max-configure 10
ipcp-max-failure 10